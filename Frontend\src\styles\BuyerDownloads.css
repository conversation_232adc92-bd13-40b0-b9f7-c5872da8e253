.BuyerDownloads {
  display: flex;
  flex-direction: column;
  width: 100%;
}

/* Table Styles - Following BuyerAccountDashboard pattern */
.BuyerDownloads .table {
  width: 100%;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.BuyerDownloads .table-header {
  display: grid;
  grid-template-columns: 0.5fr 1fr 3fr 1.2fr 1fr 1.2fr 1fr;
  background-color: var(--bg-gray);
  padding: var(--smallfont) var(--basefont);
  font-weight: 600;
  color: var(--secondary-color);
  border-bottom: 1px solid var(--light-gray);
}

.BuyerDownloads .table-row {
  display: grid;
  grid-template-columns: 0.5fr 1fr 3fr 1.2fr 1fr 1.2fr 1fr;
  padding: var(--smallfont) var(--basefont);
  border-bottom: 1px solid var(--light-gray);
  align-items: center;
}

.BuyerDownloads .table-row:last-child {
  border-bottom: none;
}

.BuyerDownloads .table-cell {
  padding: 0 var(--extrasmallfont);
  font-size: var(--smallfont);
  text-align: center; /* Center all content as requested */
}

.BuyerDownloads .content-item {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
 
}

.BuyerDownloads .content-image {
  width: 50px;
  height: 50px;
  border-radius: var(--border-radius);
  overflow: hidden;
  flex-shrink: 0;
}

.BuyerDownloads .content-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.BuyerDownloads .content-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-gray);
  border-radius: var(--border-radius);
}

.BuyerDownloads .file-icon {
  font-size: 20px;
}

.BuyerDownloads .file-icon.video {
  color: #e74c3c;
}

.BuyerDownloads .file-icon.pdf {
  color: #e67e22;
}

.BuyerDownloads .file-icon.audio {
  color: #9b59b6;
}

.BuyerDownloads .file-icon.image {
  color: #3498db;
}

.BuyerDownloads .file-icon.default {
  color: var(--dark-gray);
}

.BuyerDownloads .content-info {
  display: flex;
  flex-direction: column;
  text-align: left; /* Keep text left-aligned within the centered container */
}

.BuyerDownloads .content-title {
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.BuyerDownloads .content-coach {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
}

.BuyerDownloads .content-meta {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
  margin-top: 2px;
}

.BuyerDownloads .file-type {
  text-transform: uppercase;
  font-weight: 500;
}

.BuyerDownloads .download-count {
  font-weight: 500;
  color: var(--text-color);
}

.BuyerDownloads .last-download {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
  margin-top: 2px;
}

.BuyerDownloads .status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: var(--border-radius);
  font-size: var(--extrasmallfont);
  font-weight: 500;
  text-align: center;
}

.BuyerDownloads .status-badge.downloaded {
  background-color: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.BuyerDownloads .status-badge.pending {
  background-color: rgba(243, 156, 18, 0.1);
  color: #f39c12;
}

.BuyerDownloads .actions {
  display: flex;
  gap: var(--extrasmallfont);
  justify-content: center;
}

.BuyerDownloads .action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  color: var(--dark-gray);
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: var(--basefont);
  padding: 6px;
  border-radius: var(--border-radius);
  min-width: 32px;
  height: 32px;
}

.BuyerDownloads .action-btn:hover {
  color: var(--btn-color);
  background-color: rgba(var(--btn-color-rgb), 0.1);
}

.BuyerDownloads .action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.BuyerDownloads .download-btn {
  color: #27ae60;
}

.BuyerDownloads .download-btn:hover {
  color: #2ecc71;
  background-color: rgba(46, 204, 113, 0.1);
}

.BuyerDownloads .download-btn.downloading {
  color: #f39c12;
}

.BuyerDownloads .view-btn {
  color: #3498db;
}

.BuyerDownloads .view-btn:hover {
  color: #2980b9;
  background-color: rgba(52, 152, 219, 0.1);
}

.BuyerDownloads .spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.BuyerDownloads__empty {
  text-align: center;
  padding: var(--heading4);
  color: var(--dark-gray);
}

.BuyerDownloads__empty p {
  font-size: var(--basefont);
}

/* Responsive styles */
@media (max-width: 992px) {
  .BuyerDownloads .table-header,
  .BuyerDownloads .table-row {
    grid-template-columns: 0.5fr 1fr 2fr 1fr 1fr 1fr 1fr;
  }

  .BuyerDownloads .content-title {
    max-width: 150px;
  }
}

@media (max-width: 768px) {
  .BuyerDownloads .table {
    overflow-x: auto;
  }

  .BuyerDownloads .table-header,
  .BuyerDownloads .table-row {
    min-width: 800px;
    grid-template-columns: 0.5fr 1fr 2.5fr 1fr 1fr 1fr 1fr;
  }

  .BuyerDownloads .actions {
    flex-direction: column;
    gap: 4px;
  }

  .BuyerDownloads .action-btn {
    font-size: var(--smallfont);
    min-width: 28px;
    height: 28px;
  }
}
