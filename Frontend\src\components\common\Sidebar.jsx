import { Link, useLocation, useNavigate } from "react-router-dom";
import { useEffect, useRef, useState } from "react";
import "../../styles/Sidebar.css";
import { MdDashboard } from "react-icons/md";
import { FaUser, FaDownload, FaGavel, FaCreditCard } from "react-icons/fa";
import { MdRequestPage, MdVideoLibrary } from "react-icons/md";
import { IoLogOut } from "react-icons/io5";
import { useDispatch } from "react-redux";
import { setActiveTab } from "../../redux/slices/buyerDashboardSlice";
import { setActiveTab as setSellerActiveTab } from "../../redux/slices/sellerDashboardSlice";
import { logout } from "../../redux/slices/authSlice";
import logo from "../../assets/images/XOsports-hub-logo.svg";
import { handleBuyNavigation, handleSellNavigation } from "../../utils/navigationUtils";

const Sidebar = ({ isOpen, toggleSidebar, userRole }) => {
  const location = useLocation();
  const path = location.pathname;
  const sidebarRef = useRef(null);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [accountDropdownOpen, setAccountDropdownOpen] = useState(false);
  const [sellerAccountDropdownOpen, setSellerAccountDropdownOpen] = useState(false);

  // Close sidebar when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        sidebarRef.current &&
        !sidebarRef.current.contains(event.target) &&
        isOpen
      ) {
        toggleSidebar();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen, toggleSidebar]);

  // Close sidebar when clicking a link
  const handleLinkClick = () => {
    toggleSidebar();
  };

  // Handle account navigation
  const handleAccountNavigation = (tab) => {
    if (tab === "logout") {
      // Handle logout logic
      dispatch(logout());
      navigate("/");
    } else {
      dispatch(setActiveTab(tab));
    }
    toggleSidebar();
  };

  // Handle seller dropdown navigation
  const handleSellerDropdownNavigation = (tab) => {
    dispatch(setSellerActiveTab(tab));
    // Navigate to the corresponding route
    switch (tab) {
      case "dashboard":
        navigate("/seller/dashboard");
        break;
      case "my-sports-strategies":
        navigate("/seller/my-sports-strategies");
        break;
      case "requests":
        navigate("/seller/requests");
        break;
      case "bids":
        navigate("/seller/bids");
        break;
      case "cards":
        navigate("/seller/cards");
        break;
      case "profile":
        navigate("/seller/profile");
        break;
      default:
        navigate("/seller/dashboard");
    }
    toggleSidebar();
  };

  // Handle seller logout
  const handleSellerLogout = () => {
    dispatch(logout());
    navigate("/");
    toggleSidebar();
  };

  // Handle Buy tab click with authentication and role checks
  const handleBuyClick = (e) => {
    e.preventDefault();
    const success = handleBuyNavigation(navigate);
    if (success) {
      toggleSidebar();
    }
  };

  // Handle Sell tab click with authentication and role checks
  const handleSellClick = (e) => {
    e.preventDefault();
    const success = handleSellNavigation(navigate);
    if (success) {
      toggleSidebar();
    }
  };

  return (
    <div
      className={`sidebar-component sidebar-container ${
        isOpen ? "active" : ""
      }`}
    >
      <div className="sidebar-overlay" onClick={toggleSidebar}></div>
      <div className="sidebar" ref={sidebarRef}>
        <div className="sidebar-header">
          <div className="sidebar-logo">
            <Link to="/" onClick={handleLinkClick}>
              <img src={logo} alt="XO Sports Hub Logo" />
            </Link>
          </div>
        </div>

        <div className="sidebar-links">
          {userRole === "visitor" && (
            // Visitor links
            <>
              <Link
                to="/"
                className={path === "/" ? "active" : ""}
                onClick={handleLinkClick}
              >
                Home
              </Link>

              <Link
                to="/contact"
                className={path === "/contact" ? "active" : ""}
                onClick={handleLinkClick}
              >
                Contact Us
              </Link>
              <a
                href="#"
                className={path.startsWith("/buyer") ? "active" : ""}
                onClick={handleBuyClick}
              >
                Buy
              </a>
              <a
                href="#"
                className={path.startsWith("/seller") ? "active" : ""}
                onClick={handleSellClick}
              >
                Sell
              </a>
            </>
          )}

          {userRole === "buyer" && (
            // Buyer links
            <>
              <Link
                to="/buyer/dashboard"
                className={path === "/buyer/dashboard" ? "active" : ""}
                onClick={handleLinkClick}
              >
                Dashboard
              </Link>



              {/* Account dropdown section */}
              <div className="sidebar-account-section">
                <div
                  className="sidebar-account-header"
                  onClick={() => setAccountDropdownOpen(!accountDropdownOpen)}
                >
                  <span>My Account</span>
                  <span className={`sidebar-dropdown-icon ${accountDropdownOpen ? 'active' : ''}`}>
                    ▼
                  </span>
                </div>

                {accountDropdownOpen && (
                  <div className="sidebar-account-dropdown">
                    <Link
                      to="/buyer/account/dashboard"
                      className={path.includes("/account/dashboard") ? "active" : ""}
                      onClick={() => handleAccountNavigation("dashboard")}
                    >
                      <MdDashboard className="sidebar-icon" />
                      <span>Dashboard</span>
                    </Link>

                    <Link
                      to="/buyer/account/profile"
                      className={path.includes("/profile") ? "active" : ""}
                      onClick={() => handleAccountNavigation("profile")}
                    >
                      <FaUser className="sidebar-icon" />
                      <span>My Profile</span>
                    </Link>

                    <Link
                      to="/buyer/account/bids"
                      className={path.includes("/bids") ? "active" : ""}
                      onClick={() => handleAccountNavigation("bids")}
                    >
                      <FaGavel className="sidebar-icon" />
                      <span>My Bids</span>
                    </Link>

                    <Link
                      to="/buyer/account/downloads"
                      className={path.includes("/downloads") ? "active" : ""}
                      onClick={() => handleAccountNavigation("downloads")}
                    >
                      <FaDownload className="sidebar-icon" />
                      <span>My Downloads</span>
                    </Link>

                    <Link
                      to="/buyer/account/requests"
                      className={path.includes("/requests") ? "active" : ""}
                      onClick={() => handleAccountNavigation("requests")}
                    >
                      <MdRequestPage className="sidebar-icon" />
                      <span>My Requests</span>
                    </Link>

                    <Link
                      to="/buyer/account/cards"
                      className={path.includes("/cards") ? "active" : ""}
                      onClick={() => handleAccountNavigation("cards")}
                    >
                      <FaCreditCard className="sidebar-icon" />
                      <span>My Cards</span>
                    </Link>
                  </div>
                )}
              </div>
            </>
          )}

          {userRole === "seller" && (
            // Seller links with dropdown
            <>
              {/* Account dropdown section */}
              <div className="sidebar-account-section">
                <div
                  className="sidebar-account-header"
                  onClick={() => setSellerAccountDropdownOpen(!sellerAccountDropdownOpen)}
                >
                  <span>My Account</span>
                  <span className={`sidebar-dropdown-icon ${sellerAccountDropdownOpen ? 'active' : ''}`}>
                    ▼
                  </span>
                </div>

                {sellerAccountDropdownOpen && (
                  <div className="sidebar-account-dropdown">
                    <Link
                      to="/seller/dashboard"
                      className={path === "/seller/dashboard" ? "active" : ""}
                      onClick={() => handleSellerDropdownNavigation("dashboard")}
                    >
                      <MdDashboard className="sidebar-icon" />
                      <span>Dashboard</span>
                    </Link>

                    <Link
                      to="/seller/profile"
                      className={path === "/seller/profile" ? "active" : ""}
                      onClick={() => handleSellerDropdownNavigation("profile")}
                    >
                      <FaUser className="sidebar-icon" />
                      <span>My Profile</span>
                    </Link>

                    <Link
                      to="/seller/my-sports-strategies"
                      className={path === "/seller/my-sports-strategies" ? "active" : ""}
                      onClick={() => handleSellerDropdownNavigation("my-sports-strategies")}
                    >
                      <MdVideoLibrary className="sidebar-icon" />
                      <span>My Sports Strategies</span>
                    </Link>

                    <Link
                      to="/seller/requests"
                      className={path === "/seller/requests" ? "active" : ""}
                      onClick={() => handleSellerDropdownNavigation("requests")}
                    >
                      <MdRequestPage className="sidebar-icon" />
                      <span>Requests</span>
                    </Link>

                    <Link
                      to="/seller/bids"
                      className={path === "/seller/bids" ? "active" : ""}
                      onClick={() => handleSellerDropdownNavigation("bids")}
                    >
                      <FaGavel className="sidebar-icon" />
                      <span>My Bids</span>
                    </Link>

                    <Link
                      to="/seller/cards"
                      className={path === "/seller/cards" ? "active" : ""}
                      onClick={() => handleSellerDropdownNavigation("cards")}
                    >
                      <FaCreditCard className="sidebar-icon" />
                      <span>My Cards</span>
                    </Link>
                  </div>
                )}
              </div>
            </>
          )}
        </div>

        <div className="sidebar-auth">
          {userRole === "visitor" && (
            <>
              <Link
                to="/auth"
                className="btn signinbtn"
                onClick={handleLinkClick}
              >
                Sign In
              </Link>
              <Link
                to="/signup"
                className="btn signupbtn"
                onClick={handleLinkClick}
              >
                Sign Up
              </Link>
            </>
          )}

          {/* Show logout for all authenticated users */}
          {userRole !== "visitor" && userRole !== "admin" && (
            <Link
              to="/"
              className="btn btn-outline"
              onClick={() => handleAccountNavigation("logout")}
            >
              <IoLogOut style={{ marginRight: '8px' }} />
              Logout
            </Link>
          )}

          {userRole === "admin" && (
            <button className="btn btn-outline" onClick={handleSellerLogout}>
              <IoLogOut style={{ marginRight: '8px' }} />
              Logout
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
