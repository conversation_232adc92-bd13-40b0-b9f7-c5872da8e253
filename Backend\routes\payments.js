const express = require('express');
const { check } = require('express-validator');
const {
  getPayments,
  getPayment,
  createPaymentIntent,
  confirmPayment,
  webhook,
  getBuyerPayments,
  getSellerPayments,
  processPayout
} = require('../controllers/payments');

const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

// Public routes
router.post('/webhook', express.raw({ type: 'application/json' }), webhook);

// Protected routes
router.use(protect);

// User routes (accessible to all authenticated users)
router.get('/buyer', getBuyerPayments);

router.post(
  '/create-intent',
  [
    check('orderId', 'Order ID is required').not().isEmpty()
  ],
  createPaymentIntent
);

router.post(
  '/confirm',
  [
    check('paymentIntentId', 'Payment intent ID is required').not().isEmpty(),
    check('orderId', 'Order ID is required').not().isEmpty()
  ],
  confirmPayment
);

router.get('/seller', getSellerPayments);

// Admin routes (keep admin-only for administrative functions)
router.get('/', authorize('admin'), getPayments);
router.post('/:id/payout', authorize('admin'), processPayout);

// Common routes
router.get('/:id', getPayment);

module.exports = router;
