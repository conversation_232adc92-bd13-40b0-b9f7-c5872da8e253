const express = require('express');
const { check } = require('express-validator');
const {
  getBids,
  getBid,
  createBid,
  cancelBid,
  getContentBids,
  getUserBids,
  endAuction
} = require('../controllers/bids');

const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

// Public routes
router.get('/content/:contentId', getContentBids);

// Protected routes
router.use(protect);

// User routes (accessible to all authenticated users)
router.post(
  '/',
  [
    check('contentId', 'Content ID is required').not().isEmpty(),
    check('amount', 'Amount is required and must be a positive number').isFloat({ min: 0.01 })
  ],
  createBid
);

router.put('/:id/cancel', cancelBid);
router.get('/user', getUserBids);
router.put('/end-auction/:contentId', endAuction);

// Admin routes (keep admin-only for administrative functions)
router.get('/', authorize('admin'), getBids);

// Common routes
router.get('/:id', getBid);

module.exports = router;
