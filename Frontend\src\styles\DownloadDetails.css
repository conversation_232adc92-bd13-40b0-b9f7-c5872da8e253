.DownloadDetails {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: var(--heading5);
}

.DownloadDetails .DownloadDetails__wrapper {
  border-radius: var(--border-radius);

  overflow-x: scroll;
}

.DownloadDetails .DownloadDetails__wrapper::-webkit-scrollbar {
  display: none;
}

/* Header with seller layout pattern */
.DownloadDetails .DownloadDetails__wrapper .bordrdiv {
  border-bottom: 1px solid #fddcdc;
  display: flex;
  justify-content: space-between;
}

.DownloadDetails .DownloadDetails__header-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--heading5);
}

.DownloadDetails__download-btn {
  display: flex;
  align-items: center;
  gap: var(--extrasmallfont);
  background-color: var(--btn-color);
  color: white;
  border: none;
  padding: var(--extrasmallfont) var(--basefont);
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: var(--smallfont);
  font-weight: 500;
  transition: all 0.3s ease;
}

.DownloadDetails__download-btn:hover:not(:disabled) {
  background-color: var(--btn-hover-color);
  transform: translateY(-1px);
}

.DownloadDetails__download-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.DownloadDetails__download-btn .spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.DownloadDetails .DownloadDetails__back-btn {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  background: none;
  border: none;
  color: #0a0033;
  font-size: var(--basefont);
  cursor: pointer;
  padding: var(--smallfont) var(--basefont);
  font-weight: 600;
  border-radius: var(--border-radius);
  background-color: #fddcdc;
  border-top-left-radius: 6px;
  border-top-right-radius: 20px;
  border-bottom-left-radius: 0px;
  clip-path: polygon(0 0, 95% 0, 100% 100%, 0% 100%);
}

.DownloadDetails .DownloadDetails__header-container h3 {
  font-size: var(--heading5);
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

/* Utility class for margin-bottom */
.DownloadDetails .mb-30 {
  margin-bottom: 30px;
}

/* Content Info */
.DownloadDetails .DownloadDetails__content-info {
  display: flex;
  align-items: center;
  gap: var(--basefont);
  margin-bottom: var(--heading5);
}

.DownloadDetails .DownloadDetails__content-image {
  width: 80px;
  height: 60px;
  border-radius: var(--border-radius);
  overflow: hidden;
  flex-shrink: 0;
  position: relative;
}

.DownloadDetails .DownloadDetails__content-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.DownloadDetails .DownloadDetails__content-image .content-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-gray);
  border-radius: var(--border-radius);
}

.DownloadDetails .DownloadDetails__content-image .file-icon {
  font-size: 20px;
  color: var(--dark-gray);
}

.DownloadDetails .DownloadDetails__content-image .file-icon.video {
  color: #e74c3c;
}

.DownloadDetails .DownloadDetails__content-image .file-icon.pdf {
  color: #e67e22;
}

.DownloadDetails .DownloadDetails__content-image .file-icon.audio {
  color: #9b59b6;
}

.DownloadDetails .DownloadDetails__content-image .file-icon.image {
  color: #3498db;
}

.DownloadDetails .DownloadDetails__content-details {
  display: flex;
  flex-direction: column;
  gap: var(--extrasmallfont);
}

.DownloadDetails .DownloadDetails__content-title {
  font-size: var(--basefont);
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.DownloadDetails .DownloadDetails__content-coach {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  margin: 0;
}

.DownloadDetails .DownloadDetails__content-meta {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
  margin-top: 4px;
}

.DownloadDetails .DownloadDetails__content-meta .file-type {
  text-transform: uppercase;
  font-weight: 500;
}

.DownloadDetails .status-badge {
  display: inline-block;
  padding: 2px 8px;
  border-radius: var(--border-radius);
  font-size: var(--extrasmallfont);
  font-weight: 500;
  text-align: center;
}

.DownloadDetails .status-badge.completed {
  background-color: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}
.DownloadDetails .DownloadDetails__card {
  background-color: #ffffff; /* White background */
  border-radius: 16px; /* Figma border radius */
  padding: 24px; /* Figma padding */
  display: flex;
  flex-direction: column;
  gap: 24px; /* Figma gap */
  box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.05); /* Subtle shadow */
  border: 1px solid #e0e0e0; /* Light gray border as in Figma */
}

.DownloadDetails .DownloadDetails__card-section {
  display: grid;
  gap: 20px; /* Adjusted gap */
}

.DownloadDetails .DownloadDetails__card-section--order {
  /* Adjusted for 2 main content blocks + 1 vertical line */
  grid-template-columns: 1fr;
  align-items: flex-start;
  /* margin-bottom: 24px; */ /* Gap is handled by parent */
}

.DownloadDetails .DownloadDetails__card-section--details {
  grid-template-columns: 1fr auto 1fr; /* Customer details | vertical line | Payment details */
  align-items: flex-start;
}

.DownloadDetails .DownloadDetails__card-info-block {
  display: flex;
  flex-direction: column;
  gap: 12px; /* Figma uses a slightly larger gap between subtitle and details */
  justify-content: flex-start;
}

.DownloadDetails .DownloadDetails__card-subtitle {
  font-size: 16px; /* Figma font size */
  font-weight: 600; /* Figma font weight */
  color: #0a0033; /* Figma text color */
  margin-bottom: 4px; /* Reduced margin as gap is handled by parent */
}

.DownloadDetails .DownloadDetails__card-detail {
  display: flex;
  gap: 8px;
  align-items: flex-start; /* Align items to the start for multi-line values */
  font-size: 14px; /* Figma base font size for details */
}

.DownloadDetails .DownloadDetails__card-detail-label {
  font-size: 14px; /* Figma font size */
  color: #555555; /* Figma label color (darker gray) */
  font-weight: 400; /* Figma font weight */
  min-width: 90px; /* Adjusted min-width */
  flex-shrink: 0; /* Prevent shrinking */
}

.DownloadDetails .DownloadDetails__card-detail-value {
  font-size: 14px; /* Figma font size */
  color: #0a0033; /* Figma value color */
  font-weight: 500; /* Figma font weight */
  text-align: left;
}

.DownloadDetails .DownloadDetails__card-payment {
  display: flex;
  align-items: center;
  gap: 8px; /* Figma gap */
  /* margin-top: 8px; */ /* Gap handled by parent */
}

.DownloadDetails .DownloadDetails__card-payment-icon {
  width: 30px; /* Figma icon size */
  height: 18px; /* Figma icon size */
  object-fit: contain;
}

.DownloadDetails .DownloadDetails__card-payment-value {
  font-size: 14px; /* Figma font size */
  color: #0a0033; /* Figma value color */
  font-weight: 500; /* Figma font weight */
}

.DownloadDetails .vertical-line {
  width: 1px;
  display: flex;
  background-color: #e0e0e0; /* Figma divider color */
  min-height: 40px; /* Keep a minimum height */
  height: auto; /* Allow it to stretch based on content */
  align-self: stretch;
  margin: 0 20px; /* Figma margin for vertical line */
}

.DownloadDetails .DownloadDetails__card-divider {
  height: 1px;
  background: #e0e0e0; /* Figma divider color */
  width: 100%;
  margin: 0; /* Margin handled by parent gap */
}

/* Section Titles */
.DownloadDetails .DownloadDetails__section-title {
  font-size: var(--heading6);
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 var(--basefont) 0;
}

/* Order Information - Figma Design Match */
.DownloadDetails .DownloadDetails__order-info {
  margin-bottom: var(--heading5);
}

.DownloadDetails .DownloadDetails__order-info-container {
  display: grid;
  grid-template-columns: 1fr auto 1fr auto 1fr auto 1fr;
  align-items: center;

  border-radius: var(--border-radius);
}

.DownloadDetails .DownloadDetails__order-info-item {
  display: flex;
  flex-direction: column;
  gap: var(--extrasmallfont);
  text-align: center;
  padding: 0 var(--smallfont);
}

.DownloadDetails .DownloadDetails__order-info-label {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  font-weight: 500;
}

.DownloadDetails .DownloadDetails__order-info-value {
  font-size: var(--basefont);
  color: var(--text-color);
  font-weight: 600;
}

.DownloadDetails .DownloadDetails__order-info-divider {
  width: 1px;
  height: 40px;
  background-color: var(--light-gray);
  margin: 0 var(--smallfont);
}
.DownloadDetails .outerdivmain {
  display: grid !important;
  grid-template-columns: 1fr auto 1fr;
  gap: 1rem;
}

/* Customer and Payment Details Grid */
.DownloadDetails .DownloadDetails__details-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--heading5);
  margin-bottom: var(--heading5);
}

.DownloadDetails .DownloadDetails__customer-details,
.DownloadDetails .DownloadDetails__payment-details {
  padding: var(--basefont);
  border-radius: var(--border-radius);
}

.DownloadDetails .DownloadDetails__details-content {
  display: flex;
  flex-direction: column;
  gap: var(--basefont);
}

.DownloadDetails .DownloadDetails__detail-item {
  display: flex;
  flex-direction: column;
  gap: var(--extrasmallfont);
}

.DownloadDetails .DownloadDetails__detail-label {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  font-weight: 500;
}

.DownloadDetails .DownloadDetails__detail-value {
  font-size: var(--basefont);
  color: var(--text-color);
  font-weight: 500;
}

/* Payment Method */
.DownloadDetails .DownloadDetails__payment-method {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
}

.DownloadDetails .DownloadDetails__payment-icon {
  width: 32px;
  height: 20px;
  object-fit: contain;
}

.DownloadDetails .DownloadDetails__payment-text {
  font-size: var(--basefont);
  color: var(--text-color);
  font-weight: 500;
}

/* Video Section */
.DownloadDetails .DownloadDetails__video-section {
  margin-bottom: var(--heading5);
}

.DownloadDetails .DownloadDetails__video-container {
  width: 100%;
}

.DownloadDetails .DownloadDetails__video-player {
  position: relative;
  width: 100%;
  height: 400px;
  border-radius: var(--border-radius-medium);
  overflow: hidden;
  background-color: var(--black);
}

.DownloadDetails .DownloadDetails__video-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.DownloadDetails .DownloadDetails__content-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-gray);
  border-radius: var(--border-radius);
}

.DownloadDetails .DownloadDetails__content-placeholder .file-icon {
  font-size: 60px;
  color: var(--dark-gray);
}

.DownloadDetails .DownloadDetails__content-placeholder .file-icon.video {
  color: #e74c3c;
}

.DownloadDetails .DownloadDetails__content-placeholder .file-icon.pdf {
  color: #e67e22;
}

.DownloadDetails .DownloadDetails__content-placeholder .file-icon.audio {
  color: #9b59b6;
}

.DownloadDetails .DownloadDetails__content-placeholder .file-icon.image {
  color: #3498db;
}

.DownloadDetails .DownloadDetails__play-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.DownloadDetails .DownloadDetails__play-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background-color: rgba(238, 52, 37, 0.9);
  border: none;
  border-radius: 50%;
  color: var(--white);
  font-size: var(--heading4);
  cursor: pointer;
  transition: all 0.3s ease;
}

.DownloadDetails .DownloadDetails__play-btn:hover:not(:disabled) {
  background-color: var(--btn-color);
  transform: scale(1.1);
}

.DownloadDetails .DownloadDetails__play-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.DownloadDetails .DownloadDetails__play-btn .spinning {
  animation: spin 1s linear infinite;
}

.DownloadDetails .DownloadDetails__video-title-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: var(--heading5) var(--basefont) var(--basefont);
}

.DownloadDetails .DownloadDetails__video-title {
  color: var(--white);
  font-size: var(--heading5);
  font-weight: 600;
  margin: 0;
  text-align: center;
}

/* Description */
.DownloadDetails .DownloadDetails__description-section {
  margin-bottom: var(--heading5);
}

.DownloadDetails .DownloadDetails__description-text {
  font-size: var(--basefont);
  line-height: 1.6;
  color: var(--text-color);
  margin: 0;
}

.DownloadDetails .DownloadDetails__strategic-content {
  margin-top: var(--basefont);
  padding-top: var(--basefont);
  border-top: 1px solid var(--light-gray);
}

.DownloadDetails .DownloadDetails__strategic-content h4 {
  font-size: var(--basefont);
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 var(--smallfont) 0;
}

.DownloadDetails .DownloadDetails__strategic-content p {
  font-size: var(--basefont);
  line-height: 1.6;
  color: var(--text-color);
  margin: 0;
}

/* Error State */
.DownloadDetails .DownloadDetails__error {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: var(--error-color);
  font-size: var(--heading6);
}

/* Responsive Design */
@media (max-width: 768px) {
  .DownloadDetails .DownloadDetails__wrapper {
    padding: var(--basefont);
  }

  .DownloadDetails .DownloadDetails__header-container {
    gap: var(--basefont);
  }

  .DownloadDetails .DownloadDetails__order-info-container {
    grid-template-columns: 1fr auto 1fr;
    gap: var(--smallfont);
  }
.DownloadDetails .outerdivmain {
  display: grid !important;
  grid-template-columns: 1fr;
  gap: 1rem;
}
  .DownloadDetails .DownloadDetails__order-info-item:nth-child(5),
  .DownloadDetails .DownloadDetails__order-info-divider:nth-child(4),
  .DownloadDetails .DownloadDetails__order-info-item:nth-child(7),
  .DownloadDetails .DownloadDetails__order-info-divider:nth-child(6) {
    display: none;
  }
.DownloadDetails .DownloadDetails__card-section--details {
  grid-template-columns: 1fr; /* Customer details | vertical line | Payment details */
  align-items: flex-start;
}
  .DownloadDetails .DownloadDetails__details-grid {
    grid-template-columns: 1fr;
    gap: var(--basefont);
  }

  .DownloadDetails .DownloadDetails__video-player {
    height: 250px;
  }

  .DownloadDetails .DownloadDetails__play-btn {
    width: 60px;
    height: 60px;
    font-size: var(--heading5);
  }
  .DownloadDetails .vertical-line {
display: none; /* Figma margin for vertical line */
}
}

@media (max-width: 480px) {
  .DownloadDetails .DownloadDetails__header-container {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--smallfont);
  }

  .DownloadDetails .DownloadDetails__order-info-container {
    grid-template-columns: 1fr;
    gap: var(--basefont);
  }

  .DownloadDetails .DownloadDetails__order-info-divider {
    display: none;
  }

  .DownloadDetails .DownloadDetails__order-info-item {
    text-align: left;
    padding: var(--smallfont) 0;
    border-bottom: 1px solid var(--light-gray);
  }

  .DownloadDetails .DownloadDetails__order-info-item:last-child {
    border-bottom: none;
  }

  .DownloadDetails .DownloadDetails__content-info {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--smallfont);
  }

  .DownloadDetails .DownloadDetails__video-player {
    height: 200px;
  }

  .DownloadDetails .mb-30 {
    margin-bottom: var(--basefont);
  }
}

/* Enhanced Preview Styles */

/* Enhanced Video Player */
.DownloadDetails .DownloadDetails__video-container-enhanced {
  position: relative;
  width: 100%;
  background: #000;
  border-radius: var(--border-radius);
  overflow: hidden;
}

.DownloadDetails .DownloadDetails__video-element {
  width: 100%;
  height: 100%;
  object-fit: contain;
  background: #000;
  cursor: pointer;
}

/* Custom Video Controls */
.DownloadDetails .DownloadDetails__video-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 20px 15px 15px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.DownloadDetails .DownloadDetails__video-container-enhanced:hover .DownloadDetails__video-controls {
  opacity: 1;
}

.DownloadDetails .DownloadDetails__video-progress-container {
  margin-bottom: 15px;
}

.DownloadDetails .DownloadDetails__video-progress-bar {
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  cursor: pointer;
  position: relative;
}

.DownloadDetails .DownloadDetails__video-progress-filled {
  height: 100%;
  background: var(--btn-color);
  border-radius: 2px;
  transition: width 0.1s ease;
}

.DownloadDetails .DownloadDetails__video-controls-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.DownloadDetails .DownloadDetails__video-controls-left,
.DownloadDetails .DownloadDetails__video-controls-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

.DownloadDetails .DownloadDetails__video-control-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background 0.3s ease;
  display: flex;
  align-items: center;
  gap: 5px;
}

.DownloadDetails .DownloadDetails__video-control-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.DownloadDetails .DownloadDetails__video-volume-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.DownloadDetails .DownloadDetails__video-volume-icon {
  color: white;
  font-size: 1rem;
}

.DownloadDetails .DownloadDetails__video-volume-slider {
  width: 80px;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  outline: none;
  cursor: pointer;
}

.DownloadDetails .DownloadDetails__video-volume-slider::-webkit-slider-thumb {
  appearance: none;
  width: 12px;
  height: 12px;
  background: var(--btn-color);
  border-radius: 50%;
  cursor: pointer;
}

.DownloadDetails .DownloadDetails__video-time {
  color: white;
  font-size: 0.9rem;
  font-weight: 500;
  min-width: 100px;
}

.DownloadDetails .DownloadDetails__video-speed-container {
  position: relative;
}

.DownloadDetails .DownloadDetails__video-speed-menu {
  position: absolute;
  bottom: 100%;
  right: 0;
  background: rgba(0, 0, 0, 0.9);
  border-radius: 8px;
  padding: 8px;
  margin-bottom: 10px;
  min-width: 80px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.DownloadDetails .DownloadDetails__video-speed-option {
  display: block;
  width: 100%;
  background: none;
  border: none;
  color: white;
  padding: 8px 12px;
  text-align: center;
  cursor: pointer;
  border-radius: 4px;
  transition: background 0.3s ease;
  font-size: 0.9rem;
}

.DownloadDetails .DownloadDetails__video-speed-option:hover {
  background: rgba(255, 255, 255, 0.2);
}

.DownloadDetails .DownloadDetails__video-speed-option.active {
  background: var(--btn-color);
  color: white;
}

/* PDF Viewer */
.DownloadDetails .DownloadDetails__pdf-viewer {
  position: relative;
  width: 100%;
  height: 600px;
  background: #fff;
  border-radius: var(--border-radius);
  overflow: hidden;
}

.DownloadDetails .DownloadDetails__pdf-element {
  width: 100%;
  height: 100%;
  border: none;
  border-radius: var(--border-radius);
}

.DownloadDetails .DownloadDetails__pdf-fallback {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 0.9rem;
}

.DownloadDetails .DownloadDetails__pdf-fallback a {
  color: #4CAF50;
  text-decoration: none;
}

.DownloadDetails .DownloadDetails__pdf-fallback a:hover {
  text-decoration: underline;
}

/* Audio Player */
.DownloadDetails .DownloadDetails__audio-player {
  padding: 40px;
  text-align: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: var(--border-radius);
}

.DownloadDetails .DownloadDetails__audio-element {
  width: 100%;
  max-width: 500px;
  margin-bottom: 20px;
}

.DownloadDetails .DownloadDetails__audio-title {
  margin: 0 0 10px 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.DownloadDetails .DownloadDetails__audio-coach {
  margin: 0;
  opacity: 0.9;
  font-size: 1.1rem;
}

/* Image Viewer */
.DownloadDetails .DownloadDetails__image-viewer {
  position: relative;
  width: 100%;
  max-height: 600px;
  background: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--border-radius);
  overflow: hidden;
}

.DownloadDetails .DownloadDetails__image-element {
  max-width: 100%;
  max-height: 600px;
  object-fit: contain;
}

.DownloadDetails .DownloadDetails__image-title-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 30px 20px 20px;
  z-index: 1;
}

.DownloadDetails .DownloadDetails__image-title {
  color: white;
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

/* File Preview */
.DownloadDetails .DownloadDetails__file-preview {
  padding: 60px 40px;
  text-align: center;
  background: #f8f9fa;
  border-radius: var(--border-radius);
}

.DownloadDetails .DownloadDetails__file-icon-large {
  font-size: 5rem;
  color: #666;
  margin-bottom: 20px;
}

.DownloadDetails .DownloadDetails__file-title {
  margin: 0 0 10px 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
}

.DownloadDetails .DownloadDetails__file-type {
  margin: 0 0 30px 0;
  color: #666;
  font-size: 1.1rem;
}

.DownloadDetails .DownloadDetails__preview-download-btn {
  background: var(--btn-color);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: var(--border-radius);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.DownloadDetails .DownloadDetails__preview-download-btn:hover:not(:disabled) {
  background: var(--btn-hover-color);
  transform: translateY(-2px);
}

.DownloadDetails .DownloadDetails__preview-download-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Responsive adjustments for enhanced preview */
@media (max-width: 768px) {
  .DownloadDetails .DownloadDetails__pdf-viewer {
    height: 400px;
  }

  .DownloadDetails .DownloadDetails__audio-player {
    padding: 30px 20px;
  }

  .DownloadDetails .DownloadDetails__file-preview {
    padding: 40px 20px;
  }

  .DownloadDetails .DownloadDetails__file-icon-large {
    font-size: 4rem;
  }

  /* Enhanced video controls responsive */
  .DownloadDetails .DownloadDetails__video-controls-left,
  .DownloadDetails .DownloadDetails__video-controls-right {
    gap: 10px;
  }

  .DownloadDetails .DownloadDetails__video-volume-slider {
    width: 60px;
  }

  .DownloadDetails .DownloadDetails__video-time {
    font-size: 0.8rem;
    min-width: 80px;
  }

  .DownloadDetails .DownloadDetails__video-control-btn {
    font-size: 1rem;
    padding: 6px;
  }
}

@media (max-width: 480px) {
  .DownloadDetails .DownloadDetails__pdf-viewer {
    height: 300px;
  }

  .DownloadDetails .DownloadDetails__audio-player {
    padding: 20px 15px;
  }

  .DownloadDetails .DownloadDetails__file-preview {
    padding: 30px 15px;
  }

  .DownloadDetails .DownloadDetails__file-icon-large {
    font-size: 3rem;
  }

  /* Enhanced video controls mobile */
  .DownloadDetails .DownloadDetails__video-controls {
    padding: 15px 10px 10px;
  }

  .DownloadDetails .DownloadDetails__video-controls-bottom {
    flex-direction: column;
    gap: 10px;
  }

  .DownloadDetails .DownloadDetails__video-controls-left,
  .DownloadDetails .DownloadDetails__video-controls-right {
    gap: 8px;
  }

  .DownloadDetails .DownloadDetails__video-volume-container {
    display: none; /* Hide volume on mobile */
  }

  .DownloadDetails .DownloadDetails__video-time {
    font-size: 0.75rem;
    min-width: 70px;
  }

  .DownloadDetails .DownloadDetails__video-speed-menu {
    right: auto;
    left: 0;
  }
}
