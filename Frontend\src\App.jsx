import { lazy, Suspense, useState, useEffect } from "react";
import { Routes, Route } from "react-router-dom";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import Footer from "../src/components/common/Footer";

// Common Components
import Navbar from "./components/common/Navbar";
import ScrollToTop from "./components/common/ScrollToTop";
import ScrollToTopOnRouteChange from "./components/common/ScrollToTopOnRouteChange";
import Preloader from "./components/common/Preloader";
import ProtectedRoute from "./components/common/ProtectedRoute";

// Lenis Smooth Scrolling Provider
import LenisProvider from "./utils/LenisProvider";

// Import and preload Lottie animation data
import preloaderAnimation from "./assets/preloader-animation.json";

// Preload the animation data
const preloadedAnimation = { ...preloaderAnimation };

// Lazy-loaded Authentication
const Auth = lazy(() => import("./pages/Authentication/Auth"));
const Signup = lazy(() => import("./pages/Authentication/Signup"));
const OtpVerification = lazy(() =>
  import("./pages/Authentication/OtpVerification")
);

// Lazy-loaded Visitor Pages
const Home = lazy(() => import("./pages/Visitor/Home"));
const Contact = lazy(() => import("./pages/Visitor/Contact"));
const CheckoutPage = lazy(() => import("./pages/Visitor/CheckoutPage"));
const ThankYou = lazy(() => import("./pages/Visitor/ThankYou"));
const CoachProfilePage = lazy(() => import("./pages/Visitor/CoachProfilePage")); // Added import
const BidThankYou = lazy(() => import("./components/common/BidThankYou")); // Added import for BidThankYou
const RequestThankYou = lazy(() => import("./pages/Visitor/RequestThankYou")); // Added import for RequestThankYou

// Lazy-loaded Buyer Pages
const BuyerDashboard = lazy(() => import("./pages/Buyer/BuyerDashboard"));
const BuyerContentDetail = lazy(() =>
  import("./pages/Buyer/BuyerContentDetail")
);
const BuyerOrders = lazy(() => import("./pages/Buyer/BuyerOrders"));
const BuyerSettings = lazy(() => import("./pages/Buyer/BuyerSettings"));
const BuyerAccount = lazy(() => import("./pages/Buyer/BuyerAccount"));
const BuyerAccountDashboard = lazy(() =>
  import("./pages/Buyer/BuyerAccountDashboard")
);
//const ItemDetails = lazy(() => import("./pages/Buyer/ItemDetails"));
const DownloadDetails = lazy(() => import("./pages/Buyer/DownloadDetails")); // Added import
const BuyerCheckoutPage = lazy(() => import("./pages/Buyer/CheckoutPage"));
const PaymentSuccessPage = lazy(() => import("./pages/Buyer/PaymentSuccessPage"));

// Lazy-loaded Seller Pages
const SellerDashboard = lazy(() => import("./pages/Seller/SellerDashboard"));
const SellerMyContent = lazy(() => import("./pages/Seller/SellerMyContent"));
const SellerSettings = lazy(() => import("./pages/Seller/SellerSettings"));
const SellerMySportsStrategies = lazy(() =>
  import("./pages/Seller/SellerMySportsStrategies")
);
const AddStrategy = lazy(() => import("./pages/Seller/AddStrategy"));
const EditStrategy = lazy(() => import("./pages/Seller/EditStrategy"));
const StrategyDetails = lazy(() =>
  import("./components/seller/StrategyDetails")
);
const RequestDetails = lazy(() => import("./components/seller/RequestDetails"));
const BidDetails = lazy(() => import("./components/seller/BidDetails"));
const SellerRequests = lazy(() => import("./pages/Seller/SellerRequests"));
const SellerBids = lazy(() => import("./pages/Seller/SellerBids"));
const SellerCards = lazy(() => import("./pages/Seller/SellerCards"));
const SellerProfile = lazy(() => import("./pages/Seller/SellerProfile"));
const SellerOnboarding = lazy(() =>
  import("./components/seller/SellerOnboarding")
);

const App = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [contentLoaded, setContentLoaded] = useState(false);
  const [animationLoaded, setAnimationLoaded] = useState(false);

  // Check if both animation and content are loaded
  const checkAllLoaded = () => {
    if (animationLoaded && contentLoaded) {
      // Add a small delay for a smooth transition
      setTimeout(() => {
        setIsLoading(false);
      }, 300);
    }
  };

  // Handle animation loaded callback
  const handleAnimationLoaded = () => {
    setAnimationLoaded(true);
    checkAllLoaded();
  };

  // Handle content loaded
  useEffect(() => {
    // Listen for when the page content is fully loaded
    const handleLoad = () => {
      setContentLoaded(true);
      checkAllLoaded();
    };

    // Check if already loaded
    if (document.readyState === "complete") {
      setContentLoaded(true);
      checkAllLoaded();
    } else {
      window.addEventListener("load", handleLoad);
      return () => window.removeEventListener("load", handleLoad);
    }
  }, []);

  return (
    <LenisProvider>
      <>
        {/* Preloader */}
        <Preloader
          animationData={preloadedAnimation}
          onLoaded={handleAnimationLoaded}
          isLoading={isLoading}
        />

        <Navbar />
        {/* Automatic scroll to top on route change */}
        <ScrollToTopOnRouteChange />
        <main>
          <Suspense>
            <Routes>
              {/* Visitor Routes */}
              <Route path="/" element={<Home />} />
              <Route path="/contact" element={<Contact />} />
              <Route path="/checkout" element={<CheckoutPage />} />
              <Route path="/thank-you" element={<ThankYou />} />

              {/* New Checkout Routes */}
              <Route
                path="/checkout/:orderId"
                element={
                  <ProtectedRoute requireAuth={true}>
                    <BuyerCheckoutPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/payment-success/:orderId"
                element={
                  <ProtectedRoute requireAuth={true}>
                    <PaymentSuccessPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/coach-profile"
                element={<CoachProfilePage />}
              />{" "}
              {/* Added route */}
              <Route path="/bid-thank-you" element={<BidThankYou />} />
              <Route
                path="/request-thank-you"
                element={<RequestThankYou />}
              />{" "}
              {/* Added route */}
              {/* Authentication - Prevent access for logged-in users */}
              <Route
                path="/auth"
                element={
                  <ProtectedRoute preventAuth={true}>
                    <Auth />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/signup"
                element={
                  <ProtectedRoute preventAuth={true}>
                    <Signup />
                  </ProtectedRoute>
                }
              />
              <Route path="/otp-verification" element={<OtpVerification />} />
              {/* User Routes - Accessible to all authenticated users */}
              <Route
                path="/buyer/dashboard"
                element={
                  <ProtectedRoute requireAuth={true}>
                    <BuyerDashboard />
                  </ProtectedRoute>
                }
              />
              {/* Update the route to use BuyerContentDetail */}
              <Route
                path="/buyer/details/:id"
                element={
                  <ProtectedRoute requireAuth={true}>
                    <BuyerContentDetail />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/buyer/orders"
                element={
                  <ProtectedRoute requireAuth={true}>
                    <BuyerOrders />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/buyer/settings"
                element={
                  <ProtectedRoute requireAuth={true}>
                    <BuyerSettings />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/buyer/download-details/:id"
                element={
                  <ProtectedRoute requireAuth={true}>
                    <DownloadDetails />
                  </ProtectedRoute>
                }
              />
              {/* User Account Routes - Accessible to all authenticated users */}
              <Route
                path="/buyer/account/dashboard"
                element={
                  <ProtectedRoute requireAuth={true}>
                    <BuyerAccount />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/buyer/account/profile"
                element={
                  <ProtectedRoute requireAuth={true}>
                    <BuyerAccount />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/buyer/account/downloads"
                element={
                  <ProtectedRoute requireAuth={true}>
                    <BuyerAccount />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/buyer/account/requests"
                element={
                  <ProtectedRoute requireAuth={true}>
                    <BuyerAccount />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/buyer/account/bids"
                element={
                  <ProtectedRoute requireAuth={true}>
                    <BuyerAccount />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/buyer/account/cards"
                element={
                  <ProtectedRoute requireAuth={true}>
                    <BuyerAccount />
                  </ProtectedRoute>
                }
              />
              {/* Content Management Routes - Accessible to all authenticated users */}
              <Route
                path="/seller/dashboard"
                element={
                  <ProtectedRoute requireAuth={true}>
                    <SellerDashboard />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/seller-onboarding"
                element={
                  <ProtectedRoute requireAuth={true}>
                    <SellerOnboarding />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/seller/my-sports-strategies"
                element={
                  <ProtectedRoute requireAuth={true}>
                    <SellerMySportsStrategies />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/seller/my-sports-strategies/add"
                element={
                  <ProtectedRoute requireAuth={true}>
                    <AddStrategy />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/seller/strategy-details/:id/edit"
                element={
                  <ProtectedRoute requireAuth={true}>
                    <EditStrategy />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/seller/strategy-details/:id"
                element={
                  <ProtectedRoute requireAuth={true}>
                    <StrategyDetails />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/seller/request-details/:id"
                element={
                  <ProtectedRoute requireAuth={true}>
                    <RequestDetails />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/seller/bid-details/:id"
                element={
                  <ProtectedRoute requireAuth={true}>
                    <BidDetails />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/seller/requests"
                element={
                  <ProtectedRoute requireAuth={true}>
                    <SellerRequests />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/seller/bids"
                element={
                  <ProtectedRoute requireAuth={true}>
                    <SellerBids />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/seller/cards"
                element={
                  <ProtectedRoute requireAuth={true}>
                    <SellerCards />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/seller/profile"
                element={
                  <ProtectedRoute requireAuth={true}>
                    <SellerProfile />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/seller/my-content"
                element={
                  <ProtectedRoute requireAuth={true}>
                    <SellerMyContent />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/seller/settings"
                element={
                  <ProtectedRoute requireAuth={true}>
                    <SellerSettings />
                  </ProtectedRoute>
                }
              />
              {/* Catch-all route */}
              <Route path="*" element={<Home />} />
            </Routes>
          </Suspense>
        </main>
        {/* Footer */}
        <Footer />
        {/* Scroll to Top Button */}
        <ScrollToTop />

        {/* Toast Container */}
        <ToastContainer
          position="bottom-right"
          autoClose={5000}
          hideProgressBar={false}
          newestOnTop={false}
          closeOnClick
          rtl={false}
          pauseOnFocusLoss
          draggable
          pauseOnHover
          theme="light"
        />
      </>
    </LenisProvider>
  );
};

export default App;
