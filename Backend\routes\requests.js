const express = require('express');
const { check } = require('express-validator');
const {
  getRequests,
  getRequest,
  createRequest,
  respondToRequest,
  cancelRequest,
  submitContent,
  getBuyerRequests,
  getSellerRequests
} = require('../controllers/requests');

const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

// Protected routes
router.use(protect);

// User routes (accessible to all authenticated users)
router.get('/buyer', getBuyerRequests);
router.put('/:id/cancel', cancelRequest);

router.post(
  '/',
  [
    check('sellerId', 'Seller ID is required').not().isEmpty(),
    check('title', 'Title is required').not().isEmpty(),
    check('description', 'Description is required').not().isEmpty(),
    check('sport', 'Sport is required').not().isEmpty(),
    check('contentType', 'Content type is required').not().isEmpty(),
    check('budget', 'Budget is required and must be a positive number').isFloat({ min: 0.01 })
  ],
  createRequest
);

// User routes (accessible to all authenticated users)
router.get('/seller', getSellerRequests);

router.put(
  '/:id/respond',
  [
    check('accepted', 'Accepted status is required').isBoolean(),
    check('price', 'Price must be a positive number').optional().isFloat({ min: 0.01 }),
    check('message', 'Message is required').not().isEmpty()
  ],
  respondToRequest
);

router.post(
  '/:id/submit',
  authorize('seller'),
  [
    // Required fields for content submission
    check('fileUrl', 'File URL is required').not().isEmpty(),
    check('category', 'Category is required').not().isEmpty(),
    check('aboutCoach', 'About coach information is required').not().isEmpty(),
    check('strategicContent', 'Strategic content description is required').not().isEmpty(),

    // Optional fields that are used with default values
    check('language', 'Language is required').optional().isIn(['English', 'Spanish', 'French', 'German', 'Italian', 'Portuguese', 'Chinese', 'Japanese', 'Korean', 'Other']),

    // COMMENTED OUT - Fields not used in current frontend UI
    // check('videoLength', 'Video length must be a positive number').optional().isFloat({ min: 0 }),
    // check('prerequisites', 'Prerequisites must be an array').optional().isArray(),
    // check('learningObjectives', 'Learning objectives must be an array').optional().isArray(),
    // check('equipment', 'Equipment must be an array').optional().isArray()
  ],
  submitContent
);

// Admin routes
router.get('/', authorize('admin'), getRequests);

// Common routes
router.get('/:id', getRequest);

module.exports = router;
