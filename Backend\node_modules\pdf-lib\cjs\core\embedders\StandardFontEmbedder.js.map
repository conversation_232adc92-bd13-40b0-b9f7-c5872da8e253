{"version": 3, "file": "StandardFontEmbedder.js", "sourceRoot": "", "sources": ["../../../src/core/embedders/StandardFontEmbedder.ts"], "names": [], "mappings": ";;;AAAA,0DAKiC;AAEjC,iFAAyD;AAGzD,qCAAqD;AAOrD;;;;GAIG;AACH;IASE,8BAAoB,QAAmB,EAAE,UAAmB;QAC1D,kBAAkB;QAClB,IAAI,CAAC,QAAQ,GAAG,CACZ,QAAQ,KAAK,0BAAS,CAAC,YAAY,CAAC,CAAC,CAAC,0BAAS,CAAC,YAAY;YAC9D,CAAC,CAAC,QAAQ,KAAK,0BAAS,CAAC,MAAM,CAAO,CAAC,CAAC,0BAAS,CAAC,MAAM;gBACxD,CAAC,CAAC,0BAAS,CAAC,OAAO,CACpB,CAAC;QACF,IAAI,CAAC,IAAI,GAAG,qBAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAChC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;QACnC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC/B,CAAC;IAED;;;;OAIG;IACH,yCAAU,GAAV,UAAW,IAAY;QACrB,IAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAC7C,IAAM,QAAQ,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAC1C,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;YACvD,QAAQ,CAAC,GAAG,CAAC,GAAG,mBAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;SAC/C;QACD,OAAO,sBAAY,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;IAC5C,CAAC;IAED,gDAAiB,GAAjB,UAAkB,IAAY,EAAE,IAAY;QAC1C,IAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAC7C,IAAI,UAAU,GAAG,CAAC,CAAC;QAEnB,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;YACvD,IAAM,IAAI,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC9B,IAAM,KAAK,GAAG,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC;YAC3C,IAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;YACtE,UAAU,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC;SACpD;QAED,IAAM,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC;QAC1B,OAAO,UAAU,GAAG,KAAK,CAAC;IAC5B,CAAC;IAED,iDAAkB,GAAlB,UACE,IAAY,EACZ,OAAqC;QAArC,wBAAA,EAAA,YAAqC;QAE7B,IAAA,KAAqB,OAAO,UAAZ,EAAhB,SAAS,mBAAG,IAAI,KAAA,CAAa;QAE/B,IAAA,KAAoC,IAAI,CAAC,IAAI,EAA3C,QAAQ,cAAA,EAAE,SAAS,eAAA,EAAE,QAAQ,cAAc,CAAC;QACpD,IAAM,IAAI,GAAG,QAAQ,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC;QACrC,IAAM,OAAO,GAAG,SAAS,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC;QAEzC,IAAI,MAAM,GAAG,IAAI,GAAG,OAAO,CAAC;QAC5B,IAAI,CAAC,SAAS;YAAE,MAAM,IAAI,SAAS,IAAI,CAAC,CAAC;QAEzC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;IAChC,CAAC;IAED,iDAAkB,GAAlB,UAAmB,MAAc;QACzB,IAAA,KAAoC,IAAI,CAAC,IAAI,EAA3C,QAAQ,cAAA,EAAE,SAAS,eAAA,EAAE,QAAQ,cAAc,CAAC;QACpD,IAAM,IAAI,GAAG,QAAQ,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC;QACrC,IAAM,OAAO,GAAG,SAAS,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC;QACzC,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,GAAG,OAAO,CAAC,CAAC;IAC5C,CAAC;IAED,+CAAgB,GAAhB,UAAiB,OAAmB,EAAE,GAAY;QAChD,IAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC;YAC3B,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,OAAO;YAChB,QAAQ,EAAE,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,QAAQ;YAE1C,QAAQ,EACN,IAAI,CAAC,QAAQ,KAAK,0BAAS,CAAC,OAAO,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,SAAS;SACtE,CAAC,CAAC;QAEH,IAAI,GAAG,EAAE;YACP,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;YAC9B,OAAO,GAAG,CAAC;SACZ;aAAM;YACL,OAAO,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;SACnC;IACH,CAAC;IAEO,2CAAY,GAApB,UAAqB,SAAiB;QACpC,iDAAiD;QACjD,OAAO,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,IAAI,GAAG,CAAC;IACrD,CAAC;IAEO,iDAAkB,GAA1B,UAA2B,IAAY;QACrC,IAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpC,IAAM,MAAM,GAAY,IAAI,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QACrD,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,UAAU,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;YAC3D,IAAM,SAAS,GAAG,mBAAW,CAAC,UAAU,CAAC,GAAG,CAAC,CAAE,CAAC;YAChD,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;SAC/D;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAvGM,wBAAG,GAAG,UAAC,QAAmB,EAAE,UAAmB;QACpD,OAAA,IAAI,oBAAoB,CAAC,QAAQ,EAAE,UAAU,CAAC;IAA9C,CAA8C,CAAC;IAuGnD,2BAAC;CAAA,AAzGD,IAyGC;AAED,kBAAe,oBAAoB,CAAC"}