import { FontNames } from '@pdf-lib/standard-fonts';
export declare const values: (obj: any) => any[];
export declare const StandardFontValues: any[];
export declare const isStandardFont: (input: any) => input is FontNames;
export declare const rectanglesAreEqual: (a: {
    x: number;
    y: number;
    width: number;
    height: number;
}, b: {
    x: number;
    y: number;
    width: number;
    height: number;
}) => boolean;
//# sourceMappingURL=objects.d.ts.map
