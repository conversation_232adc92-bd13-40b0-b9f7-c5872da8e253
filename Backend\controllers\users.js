const ErrorResponse = require("../utils/errorResponse");
const User = require("../models/User");
const { validationResult } = require("express-validator");

// @desc    Get all users
// @route   GET /api/users
// @access  Private/Admin
exports.getUsers = async (req, res, next) => {
  try {
    const users = await User.find();

    res.status(200).json({
      success: true,
      count: users.length,
      data: users,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get single user
// @route   GET /api/users/:id
// @access  Private/Admin
exports.getUser = async (req, res, next) => {
  try {
    const user = await User.findById(req.params.id);

    if (!user) {
      return next(
        new ErrorResponse(`User not found with id of ${req.params.id}`, 404)
      );
    }

    res.status(200).json({
      success: true,
      data: user,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Create user
// @route   POST /api/users
// @access  Private/Admin
exports.createUser = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, errors: errors.array() });
    }

    const user = await User.create(req.body);

    res.status(201).json({
      success: true,
      data: user,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Update user
// @route   PUT /api/users/:id
// @access  Private/Admin
exports.updateUser = async (req, res, next) => {
  try {
    const user = await User.findByIdAndUpdate(req.params.id, req.body, {
      new: true,
      runValidators: true,
    });

    if (!user) {
      return next(
        new ErrorResponse(`User not found with id of ${req.params.id}`, 404)
      );
    }

    res.status(200).json({
      success: true,
      data: user,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Delete user
// @route   DELETE /api/users/:id
// @access  Private/Admin
exports.deleteUser = async (req, res, next) => {
  try {
    const user = await User.findByIdAndDelete(req.params.id);

    if (!user) {
      return next(
        new ErrorResponse(`User not found with id of ${req.params.id}`, 404)
      );
    }

    res.status(200).json({
      success: true,
      data: {},
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Update user profile
// @route   PUT /api/users/profile
// @access  Private
exports.updateProfile = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, errors: errors.array() });
    }

    // Fields to update
    const fieldsToUpdate = {
      firstName: req.body.firstName,
      lastName: req.body.lastName,
      email: req.body.email,
      mobile: req.body.mobile,
      bio: req.body.bio,
    };

    // If user is a seller, update seller info
    if (req.user.role === "seller" && req.body.sellerInfo) {
      fieldsToUpdate.sellerInfo = req.body.sellerInfo;
    }

    const user = await User.findByIdAndUpdate(req.user.id, fieldsToUpdate, {
      new: true,
      runValidators: true,
    });

    res.status(200).json({
      success: true,
      data: user,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get seller profile
// @route   GET /api/users/sellers/:id
// @access  Public
exports.getSellerProfile = async (req, res, next) => {
  try {
    const user = await User.findOne({
      _id: req.params.id,
      role: "seller",
    }).populate("content");

    if (!user) {
      return next(
        new ErrorResponse(`Seller not found with id of ${req.params.id}`, 404)
      );
    }

    res.status(200).json({
      success: true,
      data: user,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Verify seller
// @route   PUT /api/users/verify-seller/:id
// @access  Private/Admin
exports.verifySeller = async (req, res, next) => {
  try {
    const user = await User.findById(req.params.id);

    if (!user) {
      return next(
        new ErrorResponse(`User not found with id of ${req.params.id}`, 404)
      );
    }

    if (user.role !== "seller") {
      return next(new ErrorResponse(`User is not a seller`, 400));
    }

    user.isVerified = true;
    await user.save();

    res.status(200).json({
      success: true,
      data: user,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Update seller onboarding info
// @route   PUT /api/users/seller/onboarding
// @access  Private/Seller
exports.updateSellerOnboarding = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, errors: errors.array() });
    }

    // Allow all authenticated users to update onboarding info

    const {
      description,
      profilePic,
      experiences,
      minTrainingCost,
      socialLinks,
      sports,
      expertise,
      certifications,
    } = req.body;

    // Build sellerInfo update object
    const sellerInfoUpdate = {
      ...req.user.sellerInfo,
      ...(description !== undefined && { description }),
      ...(profilePic !== undefined && { profilePic }),
      ...(experiences !== undefined && { experiences }),
      ...(minTrainingCost !== undefined && { minTrainingCost }),
      ...(socialLinks !== undefined && { socialLinks }),
      ...(sports !== undefined && { sports }),
      ...(expertise !== undefined && { expertise }),
      ...(certifications !== undefined && { certifications }),
    };

    const user = await User.findByIdAndUpdate(
      req.user.id,
      { sellerInfo: sellerInfoUpdate },
      {
        new: true,
        runValidators: true,
      }
    );

    res.status(200).json({
      success: true,
      data: user.sellerInfo,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get seller onboarding status
// @route   GET /api/users/seller/onboarding
// @access  Private/Seller
exports.getSellerOnboardingStatus = async (req, res, next) => {
  try {
    // Allow all authenticated users to access onboarding info

    const user = await User.findById(req.user.id);
    const sellerInfo = user.sellerInfo || {};

    // Calculate onboarding completion percentage
    const requiredFields = [
      "description",
      "sports",
      "expertise",
      "experiences",
      "minTrainingCost",
    ];

    const completedFields = requiredFields.filter((field) => {
      if (field === "experiences") {
        return sellerInfo.experiences && sellerInfo.experiences.length > 0;
      }
      if (field === "sports" || field === "expertise") {
        return sellerInfo[field] && sellerInfo[field].length > 0;
      }
      return (
        sellerInfo[field] !== undefined &&
        sellerInfo[field] !== null &&
        sellerInfo[field] !== ""
      );
    });

    const completionPercentage = Math.round(
      (completedFields.length / requiredFields.length) * 100
    );

    res.status(200).json({
      success: true,
      data: {
        sellerInfo,
        onboardingStatus: {
          isComplete: sellerInfo.isOnboardingComplete || false,
          completionPercentage,
          completedFields: completedFields.length,
          totalFields: requiredFields.length,
          missingFields: requiredFields.filter(
            (field) => !completedFields.includes(field)
          ),
        },
      },
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Complete seller onboarding
// @route   POST /api/users/seller/complete-onboarding
// @access  Private/Seller
exports.completeSellerOnboarding = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, errors: errors.array() });
    }

    // Allow all authenticated users to complete onboarding

    const {
      description,
      profilePic,
      experiences,
      minTrainingCost,
      socialLinks,
      sports,
      expertise,
      certifications,
    } = req.body;

    const user = await User.findById(req.user.id);
    let sellerInfo = user.sellerInfo || {};

    // Update sellerInfo with provided data
    if (description !== undefined) sellerInfo.description = description;
    if (profilePic !== undefined) sellerInfo.profilePic = profilePic;
    if (experiences !== undefined) sellerInfo.experiences = experiences;
    if (minTrainingCost !== undefined)
      sellerInfo.minTrainingCost = minTrainingCost;
    if (socialLinks !== undefined) sellerInfo.socialLinks = socialLinks;
    if (sports !== undefined) sellerInfo.sports = sports;
    if (expertise !== undefined) sellerInfo.expertise = expertise;
    if (certifications !== undefined)
      sellerInfo.certifications = certifications;

    // Validate required fields for completion (2-step process)
    const requiredFields = [
      { field: "description", message: "Description is required" },
      { field: "experiences", message: "At least one experience is required" },
      {
        field: "minTrainingCost",
        message: "Minimum training cost is required",
      },
    ];

    const missingFields = [];

    for (const { field, message } of requiredFields) {
      if (field === "experiences") {
        if (!sellerInfo.experiences || sellerInfo.experiences.length === 0) {
          missingFields.push(message);
        }
      } else {
        if (!sellerInfo[field] || sellerInfo[field] === "") {
          missingFields.push(message);
        }
      }
    }

    if (missingFields.length > 0) {
      return res.status(400).json({
        success: false,
        message: "Cannot complete onboarding. Missing required fields.",
        missingFields,
      });
    }

    // Mark onboarding as complete and save all data
    sellerInfo.isOnboardingComplete = true;

    const updatedUser = await User.findByIdAndUpdate(
      req.user.id,
      {
        sellerInfo: sellerInfo,
      },
      {
        new: true,
        runValidators: true,
      }
    );

    res.status(200).json({
      success: true,
      message: "Seller onboarding completed successfully!",
      data: updatedUser.sellerInfo,
    });
  } catch (err) {
    next(err);
  }
};
